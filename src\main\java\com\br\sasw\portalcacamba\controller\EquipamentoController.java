package com.br.sasw.portalcacamba.controller;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import com.br.sasw.portalcacamba.service.EquipamentoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/equipamentos")
@RequiredArgsConstructor
public class EquipamentoController {

    private final EquipamentoService service;

    @GetMapping
    public List<EquipamentoResponse> findAll(EquipamentoFiltro filters) {
        return service.findAll(filters);
    }
}
