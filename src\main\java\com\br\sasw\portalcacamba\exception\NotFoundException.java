package com.br.sasw.portalcacamba.exception;

/**
 * Exception para casos onde um recurso não foi encontrado.
 * Deve ser usada quando uma busca por ID, chave ou outros critérios
 * não retorna nenhum resultado.
 */
public class NotFoundException extends RuntimeException {
    
    public NotFoundException(String message) {
        super(message);
    }
    
    public NotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
