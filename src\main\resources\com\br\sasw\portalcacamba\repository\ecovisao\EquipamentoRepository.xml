<mapper namespace="com.br.sasw.portalcacamba.repository.ecovisao.EquipamentoRepository">

  <select id="findAll" parameterType="com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro" resultType="com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse">
      SELECT
      e.TipoEquip as tipoEquipamento,
      c.nred,
      c.latitude,
      c.longitude,
      c.Ende as endereco,
      c<PERSON> as bairro,
      c.<PERSON><PERSON><PERSON> as cidade,
      c.<PERSON><PERSON>o as estado,
      c.CEP as cep,
      Funcion.Nome as nome,
      DATEDIFF(day, co.DtUltMov, convert(date, Getdate())) as tempoDias,
      convert(date, co.DtUltMov) as dataUltMovimento,
      co.IDEquip as id,
      e.<PERSON>ua<PERSON> as situacao,
      co.Codfil as codFil,
      Case when Isnull(clifat.Limite, 0) <= 0 then 15 else Isnull(clifat.Limite, 0) end as limite,
      Case when Isnull(c.<PERSON>ite, 0) <= 0 then 15 else Isnull(c.<PERSON>ite, 0) end as limiteLocal,
      p.<PERSON> as solicitante,
      clifat.NRed as nredFat,
      FORMAT(DATEADD(day, Case when Isnull(clifat.Limite, 0) <= 0 then 10 else Isnull(clifat.Limite, 0) end, convert(date, co.DtUltMov)),
              'dd/MM/yyyy') as dataPrevisaoColeta,
      FORMAT(convert(date, co.DtUltMov), 'dd/MM/yyyy') as dataEntrega
      FROM
      equipamentos e
      LEFT JOIN CtrOperequip co on co.IDEquip = e.IDEquip
      LEFT JOIN Clientes c ON c.codigo = co.codcli1 AND c.codfil = co.codfil
      LEFT JOIN Rt_Guias rtg on rtg.Guia = co.Guia and rtg.Serie = co.Serie
      LEFT JOIN Os_Vig ov on ov.OS = rtg.OS and ov.CodFil = co.CodFil
      LEFT JOIN Clientes clifat on clifat.Codigo = ov.CliFat and clifat.CodFil = ov.CodFil
      LEFT JOIN Pedido p on p.SeqRota = co.SeqRota and p.Parada = co.Parada
      LEFT JOIN Escala on Escala.SeqRota = co.SeqRota
      LEFT JOIN Funcion on Funcion.Matr = Escala.MatrMot

    <where>
      e.Situacao = 'A'
      AND c.Latitude <> ''
      AND c.Longitude <> ''

      <if test="codFil != null">
        AND e.codFil = #{codFil}
      </if>

      <if test="tipoEquip != null and tipoEquip.size > 0">
        AND e.TipoEquip IN
        <foreach item="item" collection="tipoEquip" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="nred != null and nred.size > 0">
        AND c.NRed IN
        <foreach item="item" collection="nred" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="bairro != null and bairro.size > 0">
        AND c.Bairro IN
        <foreach item="item" collection="bairro" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="nredFat != null and nredFat.size > 0">
        AND clifat.NRed IN
        <foreach item="item" collection="nredFat" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

      <if test="solicitante != null and solicitante.size > 0">
        AND p.Solicitante IN
        <foreach item="item" collection="solicitante" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>

    </where>
  </select>

</mapper>
