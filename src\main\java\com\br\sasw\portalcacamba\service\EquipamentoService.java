package com.br.sasw.portalcacamba.service;

import com.br.sasw.portalcacamba.entity.dto.request.EquipamentoFiltro;
import com.br.sasw.portalcacamba.entity.dto.response.EquipamentoResponse;
import com.br.sasw.portalcacamba.repository.ecovisao.EquipamentoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EquipamentoService {

    private final EquipamentoRepository repository;

    public List<EquipamentoResponse> findAll(EquipamentoFiltro filters) {

        return repository.findAll(filters);
    }
}
