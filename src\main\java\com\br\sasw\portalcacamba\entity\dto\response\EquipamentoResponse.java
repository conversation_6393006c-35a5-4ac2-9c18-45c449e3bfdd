package com.br.sasw.portalcacamba.entity.dto.response;

import lombok.Data;

import java.time.LocalDate;

@Data
public class EquipamentoResponse {

    private String tipoEquipamento;
    private String nred;
    private String latitude;
    private String longitude;
    private String endereco;
    private String bairro;
    private String cidade;
    private String estado;
    private String cep;
    private String nome;
    private Integer tempoDias;
    private LocalDate dataUltMovimento;
    private String id;
    private String situacao;
    private Integer codFil;
    private Integer limite;
    private Integer limiteLocal;
    private String solicitante;
    private String nredFat;
    private String dataPrevisaoColeta;
    private String dataEntrega;
}