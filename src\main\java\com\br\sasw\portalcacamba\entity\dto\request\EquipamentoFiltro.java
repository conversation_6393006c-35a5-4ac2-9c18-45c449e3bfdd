package com.br.sasw.portalcacamba.entity.dto.request;

import com.br.sasw.portalcacamba.entity.enums.TipoEquipamento;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class EquipamentoFiltro {
    private Integer codFil;
    private List<TipoEquipamento> tipoEquip;
    private List<String> nred;
    private List<String> bairro;
    private List<String> nredFat;
    private List<String> solicitante;
    
}
