package com.br.sasw.portalcacamba.exception;

import com.br.sasw.portalcacamba.entity.dto.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;

/**
 * Handler simples para os erros mais comuns da API
 */
@Slf4j
@RestControllerAdvice
public class ExceptionHandler {

    /**
     * Trata erros de corpo da requisição ausente ou malformado
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, WebRequest request) {
        
        log.warn("Erro de leitura da mensagem HTTP: {}", ex.getMessage());
        
        String message = "Corpo da requisição é obrigatório e deve estar em formato válido";
        String details = "Verifique se você está enviando o corpo da requisição (JSON/XML) corretamente";
        
        if (ex.getMessage().contains("Required request body is missing")) {
            message = "Corpo da requisição é obrigatório";
        } else if (ex.getMessage().contains("JSON parse error")) {
            message = "Formato JSON inválido";
            details = "Verifique a sintaxe do JSON enviado";
        } else if (ex.getMessage().contains("XML")) {
            message = "Formato XML inválido";
            details = "Verifique a sintaxe do XML enviado";
        }
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de parâmetros obrigatórios ausentes
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, WebRequest request) {
        
        log.warn("Parâmetro obrigatório ausente: {}", ex.getParameterName());
        
        String message = String.format("Parâmetro obrigatório '%s' está ausente", ex.getParameterName());
        String details = String.format("O parâmetro '%s' é obrigatório para esta requisição", ex.getParameterName());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de regra de negócio
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(
            BusinessException ex, WebRequest request) {

        log.warn("Erro de regra de negócio: {}", ex.getMessage());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(ex.getMessage())
                .details("Verifique os dados enviados")
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de recurso não encontrado
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(NotFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFoundException(
            NotFoundException ex, WebRequest request) {

        log.warn("Recurso não encontrado: {}", ex.getMessage());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.NOT_FOUND.value())
                .error(HttpStatus.NOT_FOUND.getReasonPhrase())
                .message(ex.getMessage())
                .details("O recurso solicitado não foi encontrado")
                .path(getPath(request))
                .build();

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Extrai o path da requisição
     */
    private String getPath(WebRequest request) {
        return request.getDescription(false).replace("uri=", "");
    }
}
